@import "../../../../theme/variables.scss";

.update-container {
  display: flex;
  flex-direction: column;
  height: auto; // 改为自动高度
  max-height: 80vh; // 限制最大高度为80%视口高度，避免过高
  min-height: 200px; // 设置最小高度确保基本显示
  overflow: hidden;
}

.check-update-header {
  text-align: center;
  min-height: 60px; // 使用最小高度而不是百分比
  padding: 12px 16px; // 增加内边距确保文字不被遮挡
  line-height: 1.4; // 调整行高
  width: 100%;
  color: var(--ion-color-primary-contrast);
  background-color: var(--ion-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  flex-shrink: 0; // 防止header被压缩
}

.check-update-context {
  display: flex;
  flex-direction: column;
  justify-content: flex-start; // 改为顶部对齐，避免内容被遮挡
  align-items: flex-start; // 左对齐
  flex: 1; // 使用flex: 1而不是固定高度
  white-space: pre-line;
  overflow-y: auto;
  padding: 16px; // 增加内边距
  line-height: 1.6; // 增加行高提高可读性
  font-size: 14px;
  color: #333;
  word-wrap: break-word; // 确保长文本换行
  word-break: break-word;
  min-height: 0; // 确保flex子元素可以正确收缩
}

.check-update-footer {
  border-top: 1px solid #f6f6f6;
  min-height: 60px; // 使用最小高度
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 12px 16px; // 增加内边距
  background-color: #fff;
  flex-shrink: 0; // 防止footer被压缩
}

.btn-confirm {
  width: 49%;
  text-align: center;
  color: var(--ion-color-primary);
  padding: 12px 8px; // 增加按钮内边距
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(var(--ion-color-primary-rgb), 0.1);
  }
}

.btn-cancal {
  width: 49%;
  text-align: center;
  color: #666;
  padding: 12px 8px; // 增加按钮内边距
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

// 添加响应式适配
@media (max-height: 600px) {
  .update-container {
    max-height: 70vh; // 小屏幕进一步减少高度
    min-height: 180px;
  }

  .check-update-header {
    min-height: 50px;
    padding: 8px 16px;
    font-size: 15px;
  }

  .check-update-context {
    padding: 12px;
    font-size: 13px;
  }

  .check-update-footer {
    min-height: 50px;
    padding: 8px 16px;
  }
}

@media (max-height: 500px) {
  .update-container {
    max-height: 60vh; // 更小屏幕进一步减少高度
    min-height: 160px;
  }

  .check-update-header {
    min-height: 40px;
    padding: 6px 16px;
    font-size: 14px;
  }

  .check-update-context {
    padding: 8px;
    font-size: 12px;
  }

  .check-update-footer {
    min-height: 40px;
    padding: 6px 16px;
  }
}

// 针对真机优化 - 添加更合适的高度限制
@media (max-width: 480px) {
  .update-container {
    max-height: 75vh; // 手机端限制高度
    min-height: 200px;
  }
}
