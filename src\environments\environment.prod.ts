import { Environment } from 'src/app/@core/base/environment';
export const environment: Environment = {
  mapTK: '7d930ac87cf5dc338644bec82cfb80b2',
  projectName: '巡检APP',
  production: true,
  openProxy: false,
  version: 'v1.1.7',
  // 加密配置 - 生产环境
  encryption: {
    enabled: false,        // 启用加密
    debugEnabled: false   // 禁用调试日志
  },

  /*****************159 外网地址 https********************/
  api: {
    mapUrl: 'https://**************:8225',
    ip: '**************',
    port: 8225,
    version: 't1'
  },
};
